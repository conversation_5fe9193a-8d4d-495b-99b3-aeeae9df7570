import { NextRequest } from 'next/server';
import { validate<PERSON><PERSON>A<PERSON>, validateRecoveryCodeAuth } from '@/app/api/_validators/auth.validator';
import { authController } from '@/core/controllers';
import logger from '@/utils/logger';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

/**
 * POST /api/auth/login - Authenticate user with access key or recovery code
 */
export async function POST(req: NextRequest) {
  try {
    // Parse request body to determine authentication type
    const body = await req.json().catch(() => ({}));

    // Check if this is a recovery code authentication
    if (body.recoveryCode) {
      // Validate recovery code request
      const validationResult = await validateRecoveryCodeAuth(req);
      if ('status' in validationResult) {
        return validationResult;
      }

      // Process recovery code authentication
      return authController.loginWithRecoveryCode(validationResult.data, req);
    } else {
      // Validate access key request
      const validationResult = await validate<PERSON>eyAuth(req);
      if ('status' in validationResult) {
        return validationResult;
      }

      // Process access key authentication
      return authController.login(validationResult.data, req);
    }
  } catch (error) {
    logger.error('Authentication error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}
